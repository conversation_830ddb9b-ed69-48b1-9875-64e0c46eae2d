<template>
  <div>
    <!-- 如果配置了 dropdown，则显示下拉菜单模式 -->
    <template v-if="dropdownConfig">
      <!-- 主要操作按钮 -->
      <ElButton
        v-if="dropdownConfig.primaryAction && hasAccessByCodes(dropdownConfig.primaryAction.auth ?? []) && getShow(dropdownConfig.primaryAction.show)"
        :type="getString(dropdownConfig.primaryAction.type ?? 'primary') as 'default' | 'primary' | 'success' | 'warning' | 'info' | 'danger' | 'text' || 'primary'"
        :size="getButtonSize(dropdownConfig.primaryAction.size)"
        @click="handleAction(dropdownConfig.primaryAction.callback, dropdownConfig.primaryAction.eventName)"
        :class="getString(dropdownConfig.primaryAction.class ?? '')"
        :disabled="getDisabled(dropdownConfig.primaryAction.disabled)"
      >
        {{ getLabel(dropdownConfig.primaryAction) }}
      </ElButton>

      <!-- 下拉菜单 -->
      <ElDropdown
        v-if="dropdownMenuItems.length > 0"
        @command="handleDropdownCommand"
        trigger="click"
        placement="bottom-end"
      >
        <ElButton
          type="default"
          circle
          style="width: auto;"
        >
          <ElIcon>
            <ArrowDown />
          </ElIcon>
        </ElButton>

        <template #dropdown>
          <ElDropdownMenu>
            <template v-for="(item, index) in dropdownMenuItems" :key="index">
              <ElDropdownItem
                v-if="hasAccessByCodes(item.auth ?? []) && getShow(item.show)"
                :command="index"
                :divided="item.divided"
                :disabled="getDisabled(item.disabled)"
              >
                {{ getLabel(item) }}
              </ElDropdownItem>
            </template>
          </ElDropdownMenu>
        </template>
      </ElDropdown>
    </template>

    <!-- 原有的按钮模式 -->
    <template v-else>
      <template v-for="(action, index) in actionList" :key="index">
        <ElPopconfirm
          v-if="action.popConfirm && hasAccessByCodes(action.auth ?? []) && getShow(action.show)"
          v-bind="getPopConfirmProps(action.popConfirm)"
        >
          <template #reference>
            <ElButton
              :type="getString(action.type ?? 'default') as 'default' | 'primary' | 'success' | 'warning' | 'info' | 'danger' | 'text' || 'default'"
              :size="getButtonSize(action.size)"
              :class="getString(action.class ?? '')"
              :disabled="getDisabled(action.disabled)"
            >
              {{ getLabel(action) }}
            </ElButton>
          </template>
        </ElPopconfirm>
        <ElButton
          v-else-if="hasAccessByCodes(action.auth ?? []) && getShow(action.show)"
          :type="getString(action.type ?? 'default') as 'default' | 'primary' | 'success' | 'warning' | 'info' | 'danger' | 'text' || 'default'"
          :size="getButtonSize(action.size)"
          @click="handleAction(action.callback, action.eventName)"
          :class="getString(action.class ?? '')"
          :disabled="getDisabled(action.disabled)"
        >
          {{ getLabel(action) }}
        </ElButton>
      </template>
    </template>
  </div>
</template>

<script lang="ts" setup>
import { ElButton, ElPopconfirm, ElDropdown, ElDropdownMenu, ElDropdownItem, ElIcon } from 'element-plus';
import { defineProps, defineEmits, computed, h } from 'vue';
import { type ICellRendererParams } from 'ag-grid-community';
import { useAccess } from '@vben/access';
import { isFunction } from '@vben/utils';

// 使用内联 SVG 图标
const ArrowDown = () => h('svg', {
  viewBox: '0 0 1024 1024',
  width: '1em',
  height: '1em',
  fill: 'currentColor'
}, [
  h('path', {
    d: 'M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z'
  })
]);

const { hasAccessByCodes } = useAccess();

interface PopConfirm {
  title?: string;
  description?: string;
  confirm?: Function;
  cancel?: Function;
  confirmButtonText?: string;
  cancelButtonText?: string;
  icon?: string;
  [key: string]: any;
}

interface Action {
  label: string | ((params: ICellRendererParams) => string);
  callback: Function;
  auth: string[];
  type?: string | ((params: ICellRendererParams) => string);
  size?: 'small' | 'default' | 'large';
  class?: string | ((params: ICellRendererParams) => string);
  eventName?: string;
  popConfirm?: PopConfirm;
  disabled?: boolean | ((params: ICellRendererParams) => boolean);
  show?: boolean | ((data: any) => boolean);
  divided?: boolean; // 下拉菜单项分割线
}

// 下拉菜单配置接口
interface DropdownConfig {
  label?: string; // 下拉按钮文本，默认"更多"
  size?: 'small' | 'default' | 'large';
  primaryAction?: Action; // 主要操作按钮
  menuItems: Action[]; // 下拉菜单项
}

interface ActionCellProps {
  params: ICellRendererParams & {
    actions?: Action[] | ((params: ICellRendererParams) => Action[]);
    dropdown?: DropdownConfig; // 下拉菜单配置
  };
}

const props = defineProps<{
  params: ActionCellProps['params'];
}>();

// 获取按钮大小，默认为 'small'
function getButtonSize(size?: 'small' | 'default' | 'large'): 'small' | 'default' | 'large' {
  return size || 'small';
}

// 定义 emit 事件
const emit = defineEmits<{
  (event: 'actionTriggered', eventName: string, data: any): void;
}>();

// 通过 props 中的 actions 配置按钮
const actionList = typeof props.params?.actions === 'function'
  ? props.params.actions(props.params)
  : props.params?.actions || [];

// 下拉菜单配置
const dropdownConfig = computed(() => props.params?.dropdown);

// 下拉菜单项（过滤掉不显示的项）
const dropdownMenuItems = computed(() => {
  if (!dropdownConfig.value?.menuItems) return [];
  return dropdownConfig.value.menuItems.filter(item =>
    hasAccessByCodes(item.auth ?? []) && getShow(item.show)
  );
});

// 处理按钮点击
function handleAction(callback: Function, eventName?: string) {
  if (callback) {
    callback(props.params.data);
  }
  if (eventName) {
    emit('actionTriggered', eventName, props.params.data);
  }
}

// 处理下拉菜单命令
function handleDropdownCommand(index: number) {
  const menuItem = dropdownMenuItems.value[index];
  if (menuItem) {
    handleAction(menuItem.callback, menuItem.eventName);
  }
}

//处理文本显示
function getLabel(action: Action) {
  if (typeof action.label === 'function') {
    return action.label(props.params);
  }
  return action.label;
}

function getString(content: string | ((params: ICellRendererParams) => string)) {
  return typeof content === 'function' ? content(props.params) : content;
}

// 处理 PopConfirm 属性
function getPopConfirmProps(attrs: PopConfirm) {
  const originAttrs: any = { ...attrs };
  if (attrs.confirm && isFunction(attrs.confirm)) {
    originAttrs.onConfirm = () => attrs.confirm?.(props.params.data);
    delete originAttrs.confirm;
  }
  if (attrs.cancel && isFunction(attrs.cancel)) {
    originAttrs.onCancel = () => attrs.cancel?.(props.params.data);
    delete originAttrs.cancel;
  }
  return originAttrs;
}

// 处理按钮禁用状态
function getDisabled(disabled?: boolean | ((params: ICellRendererParams) => boolean)) {
  if (typeof disabled === 'function') {
    return disabled(props.params);
  }
  return disabled ?? false;
}

// 处理按钮显示状态
function getShow(show?: boolean | ((data: any) => boolean)) {
  if (typeof show === 'function') {
    return show(props.params.data);
  }
  return show ?? true;
}

</script>

<style scoped>
div {
  display: flex;
  gap: 8px;
  align-items: center;
}
</style>
